package tools

import (
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/vitalii-honchar/go-agent/pkg/goagent/llm"
)

// PriorityToolParams represents parameters for priority calculation
type PriorityToolParams struct {
	TaskTitle       string     `json:"task_title" jsonschema_description:"Title of the task"`
	TaskDescription string     `json:"task_description" jsonschema_description:"Description of the task"`
	CurrentPriority string     `json:"current_priority" jsonschema_description:"Current priority level"`
	Deadline        *time.Time `json:"deadline,omitempty" jsonschema_description:"Task deadline if any"`
	Status          string     `json:"status" jsonschema_description:"Current task status"`
	CreatedAt       time.Time  `json:"created_at" jsonschema_description:"When the task was created"`
}

// PriorityToolResult represents the result of priority calculation
type PriorityToolResult struct {
	llm.BaseLLMToolResult
	SuggestedPriority string   `json:"suggested_priority" jsonschema_description:"Calculated priority level"`
	Score             int      `json:"score" jsonschema_description:"Priority score (0-100)"`
	Factors           []string `json:"factors" jsonschema_description:"Factors that influenced the priority calculation"`
	Reasoning         string   `json:"reasoning" jsonschema_description:"Explanation of the priority calculation"`
}

// CreatePriorityTool creates a new priority calculation tool for the AI agent
func CreatePriorityTool() llm.LLMTool {
	tool, err := llm.NewLLMTool(
		llm.WithLLMToolName("priority_calculator"),
		llm.WithLLMToolDescription("Calculate task priority based on various factors like deadline, keywords, and age"),
		llm.WithLLMToolParametersSchema[PriorityToolParams](),
		llm.WithLLMToolCall(func(callID string, params PriorityToolParams) (PriorityToolResult, error) {
			log.Printf("🎯 PRIORITY TOOL: Calculating priority for '%s'", params.TaskTitle)

			result := PriorityToolResult{
				BaseLLMToolResult: llm.BaseLLMToolResult{ID: callID},
				Factors:           []string{},
			}

			score := 50 // Base score
			factors := []string{}

			// Factor 1: Deadline urgency
			if params.Deadline != nil {
				timeUntilDeadline := time.Until(*params.Deadline)
				if timeUntilDeadline < 0 {
					score += 40
					factors = append(factors, "Task is overdue")
				} else if timeUntilDeadline < 24*time.Hour {
					score += 30
					factors = append(factors, "Deadline within 24 hours")
				} else if timeUntilDeadline < 3*24*time.Hour {
					score += 20
					factors = append(factors, "Deadline within 3 days")
				} else if timeUntilDeadline < 7*24*time.Hour {
					score += 10
					factors = append(factors, "Deadline within a week")
				}
			}

			// Factor 2: Keywords in title and description
			urgentKeywords := []string{"urgent", "critical", "emergency", "asap", "immediate", "bug", "fix", "issue", "problem"}
			importantKeywords := []string{"important", "priority", "client", "customer", "meeting", "presentation", "review"}

			text := params.TaskTitle + " " + params.TaskDescription
			for _, keyword := range urgentKeywords {
				if contains(text, keyword) {
					score += 15
					factors = append(factors, "Contains urgent keyword: "+keyword)
				}
			}
			for _, keyword := range importantKeywords {
				if contains(text, keyword) {
					score += 10
					factors = append(factors, "Contains important keyword: "+keyword)
				}
			}

			// Factor 3: Task age
			taskAge := time.Since(params.CreatedAt)
			if taskAge > 7*24*time.Hour {
				score += 15
				factors = append(factors, "Task is over a week old")
			} else if taskAge > 3*24*time.Hour {
				score += 10
				factors = append(factors, "Task is over 3 days old")
			}

			// Factor 4: Current status
			if params.Status == "in_progress" {
				score += 10
				factors = append(factors, "Task is already in progress")
			}

			// Ensure score is within bounds
			if score > 100 {
				score = 100
			}
			if score < 0 {
				score = 0
			}

			// Determine priority level based on score
			var priority string
			if score >= 80 {
				priority = "urgent"
			} else if score >= 65 {
				priority = "high"
			} else if score >= 40 {
				priority = "medium"
			} else {
				priority = "low"
			}

			result.SuggestedPriority = priority
			result.Score = score
			result.Factors = factors
			result.Reasoning = generateReasoning(priority, score, factors)

			log.Printf("🎯 PRIORITY RESULT: %s (score: %d)", priority, score)
			return result, nil
		}),
	)

	if err != nil {
		log.Fatalf("Failed to create priority tool: %v", err)
	}

	return tool
}

// Helper functions

func contains(text, keyword string) bool {
	// Simple case-insensitive contains check
	textLower := strings.ToLower(text)
	keywordLower := strings.ToLower(keyword)
	return strings.Contains(textLower, keywordLower)
}

func generateReasoning(priority string, score int, factors []string) string {
	reasoning := fmt.Sprintf("Priority calculated as '%s' with a score of %d/100. ", priority, score)

	if len(factors) > 0 {
		reasoning += "Key factors considered: "
		for i, factor := range factors {
			if i > 0 {
				reasoning += ", "
			}
			reasoning += factor
		}
		reasoning += "."
	} else {
		reasoning += "No special factors identified, using base priority calculation."
	}

	return reasoning
}
