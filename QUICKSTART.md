# Quick Start Guide

This guide will help you get the Task Management API with AI Agent up and running in minutes.

## Prerequisites

1. **Go 1.21+** installed on your system
2. **OpenAI API Key** - Get one from [OpenAI Platform](https://platform.openai.com/api-keys)

## Step 1: Set Environment Variables

### Windows (PowerShell)
```powershell
$env:OPENAI_API_KEY = "your-openai-api-key-here"
$env:PORT = "8080"  # Optional, defaults to 8080
```

### Linux/macOS (Bash)
```bash
export OPENAI_API_KEY="your-openai-api-key-here"
export PORT=8080  # Optional, defaults to 8080
```

## Step 2: Install Dependencies

```bash
go mod tidy
```

## Step 3: Build and Run

### Option A: Build and Run Executable
```bash
go build -o task-api.exe .
./task-api.exe
```

### Option B: Run Directly
```bash
go run main.go
```

## Step 4: Test the API

The server will start on `http://localhost:8080` and display available endpoints.

### Quick Test Commands

```bash
# Health check
curl http://localhost:8080/health

# List all tasks (includes sample data)
curl http://localhost:8080/tasks

# AI analysis of all tasks
curl -X POST http://localhost:8080/tasks/analyze

# AI priority suggestion for a specific task
curl -X POST http://localhost:8080/tasks/task_1/suggest-priority
```

### Automated Testing

Run the provided test scripts:

**Windows:**
```powershell
.\test_api.ps1
```

**Linux/macOS:**
```bash
./test_api.sh
```

## What You'll See

1. **Sample Tasks**: The API starts with 4 sample tasks demonstrating different priorities and statuses
2. **AI Analysis**: The AI agent analyzes tasks and provides insights about priority distribution, urgent items, and productivity tips
3. **Priority Suggestions**: Get AI-powered priority recommendations for individual tasks
4. **Detailed Logging**: Watch the AI agent's decision-making process in the server logs

## Key Features Demonstrated

- ✅ **REST API** for task management
- ✅ **AI-Powered Analysis** using go-agent framework
- ✅ **Custom Tools** for database operations, priority calculation, and time analysis
- ✅ **Type-Safe AI Agents** with structured output schemas
- ✅ **Intelligent Recommendations** based on multiple factors

## Next Steps

1. **Explore the Code**: Check out the different components in the project structure
2. **Add Custom Tools**: Extend the AI agent with new capabilities
3. **Integrate Database**: Replace in-memory storage with a real database
4. **Add Authentication**: Implement user authentication and authorization
5. **Deploy**: Deploy to your preferred cloud platform

## Troubleshooting

**"OPENAI_API_KEY environment variable is required"**
- Make sure you've set the OPENAI_API_KEY environment variable
- Verify the API key is valid and has sufficient credits

**"Failed to create task agent"**
- Check your internet connection
- Verify the OpenAI API key is correct
- Ensure you have sufficient API credits

**Build errors**
- Make sure you're using Go 1.21 or higher
- Run `go mod tidy` to ensure all dependencies are downloaded

## Support

For issues or questions:
1. Check the main [README.md](README.md) for detailed documentation
2. Review the [go-agent framework documentation](https://github.com/vitalii-honchar/go-agent)
3. Examine the server logs for detailed error information
