package tools

import (
	"fmt"
	"log"
	"time"

	"github.com/vitalii-honchar/go-agent/pkg/goagent/llm"
)

// TimeToolParams represents parameters for time operations
type TimeToolParams struct {
	Operation  string     `json:"operation" jsonschema_description:"Time operation: 'current_time', 'current_date', 'time_until', 'business_hours'"`
	TargetTime *time.Time `json:"target_time,omitempty" jsonschema_description:"Target time for 'time_until' operation"`
}

// TimeToolResult represents the result of time operations
type TimeToolResult struct {
	llm.BaseLLMToolResult
	CurrentTime    time.Time `json:"current_time" jsonschema_description:"Current timestamp"`
	FormattedTime  string    `json:"formatted_time" jsonschema_description:"Human-readable time format"`
	TimeUntil      string    `json:"time_until,omitempty" jsonschema_description:"Time until target (for time_until operation)"`
	IsBusinessHour bool      `json:"is_business_hour,omitempty" jsonschema_description:"Whether current time is business hours"`
	DayOfWeek      string    `json:"day_of_week" jsonschema_description:"Current day of the week"`
	Info           string    `json:"info" jsonschema_description:"Additional contextual information"`
}

// CreateTimeTool creates a new time tool for the AI agent
func CreateTimeTool() llm.LLMTool {
	tool, err := llm.NewLLMTool(
		llm.WithLLMToolName("time"),
		llm.WithLLMToolDescription("Get current time information and perform time-related calculations"),
		llm.WithLLMToolParametersSchema[TimeToolParams](),
		llm.WithLLMToolCall(func(callID string, params TimeToolParams) (TimeToolResult, error) {
			log.Printf("⏰ TIME TOOL: %s", params.Operation)

			now := time.Now()
			result := TimeToolResult{
				BaseLLMToolResult: llm.BaseLLMToolResult{ID: callID},
				CurrentTime:       now,
				FormattedTime:     now.Format("2006-01-02 15:04:05 MST"),
				DayOfWeek:         now.Weekday().String(),
			}

			switch params.Operation {
			case "current_time":
				result.Info = "Current system time"

			case "current_date":
				result.FormattedTime = now.Format("2006-01-02")
				result.Info = "Current system date"

			case "time_until":
				if params.TargetTime == nil {
					result.Info = "Error: target_time is required for time_until operation"
				} else {
					duration := params.TargetTime.Sub(now)
					result.TimeUntil = formatDuration(duration)
					if duration < 0 {
						result.Info = "Target time is in the past"
					} else {
						result.Info = "Time remaining until target"
					}
				}

			case "business_hours":
				result.IsBusinessHour = isBusinessHours(now)
				if result.IsBusinessHour {
					result.Info = "Currently within business hours (9 AM - 5 PM, Monday-Friday)"
				} else {
					result.Info = "Currently outside business hours"
				}

			default:
				result.Info = "Unknown operation: " + params.Operation
			}

			log.Printf("⏰ TIME RESULT: %s", result.FormattedTime)
			return result, nil
		}),
	)

	if err != nil {
		log.Fatalf("Failed to create time tool: %v", err)
	}

	return tool
}

// Helper functions

func formatDuration(d time.Duration) string {
	if d < 0 {
		return "overdue by " + formatDuration(-d)
	}

	days := int(d.Hours()) / 24
	hours := int(d.Hours()) % 24
	minutes := int(d.Minutes()) % 60

	if days > 0 {
		return fmt.Sprintf("%d days, %d hours, %d minutes", days, hours, minutes)
	} else if hours > 0 {
		return fmt.Sprintf("%d hours, %d minutes", hours, minutes)
	} else {
		return fmt.Sprintf("%d minutes", minutes)
	}
}

func isBusinessHours(t time.Time) bool {
	// Business hours: 9 AM - 5 PM, Monday-Friday
	weekday := t.Weekday()
	hour := t.Hour()

	return weekday >= time.Monday && weekday <= time.Friday && hour >= 9 && hour < 17
}
