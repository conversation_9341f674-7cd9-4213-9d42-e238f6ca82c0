# PowerShell script to test the Task Management API
# Make sure the server is running before executing this script

$baseUrl = "http://localhost:8080"

Write-Host "🧪 Testing Task Management API with AI Agent" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Test 1: Health Check
Write-Host "`n1. Health Check" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/health" -Method GET
    Write-Host "✅ Health check passed" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "❌ Health check failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: List all tasks
Write-Host "`n2. List All Tasks" -ForegroundColor Yellow
try {
    $tasks = Invoke-RestMethod -Uri "$baseUrl/tasks" -Method GET
    Write-Host "✅ Retrieved $($tasks.Count) tasks" -ForegroundColor Green
    $tasks | ConvertTo-Json -Depth 3
} catch {
    Write-Host "❌ Failed to retrieve tasks: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Create a new task
Write-Host "`n3. Create New Task" -ForegroundColor Yellow
$newTask = @{
    title = "Test API Integration"
    description = "Testing the task management API with PowerShell"
    priority = "medium"
    deadline = (Get-Date).AddDays(3).ToString("yyyy-MM-ddTHH:mm:ssZ")
} | ConvertTo-Json

try {
    $createdTask = Invoke-RestMethod -Uri "$baseUrl/tasks" -Method POST -Body $newTask -ContentType "application/json"
    Write-Host "✅ Created new task with ID: $($createdTask.id)" -ForegroundColor Green
    $createdTask | ConvertTo-Json -Depth 3
    $newTaskId = $createdTask.id
} catch {
    Write-Host "❌ Failed to create task: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Get specific task
if ($newTaskId) {
    Write-Host "`n4. Get Specific Task" -ForegroundColor Yellow
    try {
        $task = Invoke-RestMethod -Uri "$baseUrl/tasks/$newTaskId" -Method GET
        Write-Host "✅ Retrieved task: $($task.title)" -ForegroundColor Green
        $task | ConvertTo-Json -Depth 3
    } catch {
        Write-Host "❌ Failed to retrieve task: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 5: AI Task Analysis
Write-Host "`n5. AI Task Analysis" -ForegroundColor Yellow
Write-Host "⏳ This may take a moment as the AI analyzes all tasks..." -ForegroundColor Cyan
try {
    $analysis = Invoke-RestMethod -Uri "$baseUrl/tasks/analyze" -Method POST
    Write-Host "✅ AI analysis completed" -ForegroundColor Green
    Write-Host "📊 Analysis Results:" -ForegroundColor Cyan
    $analysis | ConvertTo-Json -Depth 4
} catch {
    Write-Host "❌ AI analysis failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 Make sure OPENAI_API_KEY is set correctly" -ForegroundColor Yellow
}

# Test 6: AI Priority Suggestion
Write-Host "`n6. AI Priority Suggestion" -ForegroundColor Yellow
Write-Host "⏳ Getting AI priority suggestion for first task..." -ForegroundColor Cyan
try {
    $prioritySuggestion = Invoke-RestMethod -Uri "$baseUrl/tasks/task_1/suggest-priority" -Method POST
    Write-Host "✅ AI priority suggestion completed" -ForegroundColor Green
    Write-Host "🎯 Priority Suggestion:" -ForegroundColor Cyan
    $prioritySuggestion | ConvertTo-Json -Depth 4
} catch {
    Write-Host "❌ Priority suggestion failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 Make sure OPENAI_API_KEY is set correctly" -ForegroundColor Yellow
}

# Test 7: Update task
if ($newTaskId) {
    Write-Host "`n7. Update Task" -ForegroundColor Yellow
    $updateData = @{
        status = "in_progress"
        priority = "high"
    } | ConvertTo-Json
    
    try {
        $updatedTask = Invoke-RestMethod -Uri "$baseUrl/tasks/$newTaskId" -Method PUT -Body $updateData -ContentType "application/json"
        Write-Host "✅ Updated task status and priority" -ForegroundColor Green
        $updatedTask | ConvertTo-Json -Depth 3
    } catch {
        Write-Host "❌ Failed to update task: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n🎉 API Testing Complete!" -ForegroundColor Green
Write-Host "Check the server logs to see the AI agent in action." -ForegroundColor Cyan
