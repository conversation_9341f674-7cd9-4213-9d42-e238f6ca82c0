# Task Management API with AI Agent

A proof of concept Golang backend demonstrating the [go-agent framework](https://github.com/vitalii-honchar/go-agent) for building AI-powered applications. This project showcases how to integrate AI agents into a traditional REST API to provide intelligent task management capabilities.

## 🌟 Features

- **REST API** for task management (CRUD operations)
- **AI-Powered Analysis** using the go-agent framework
- **Intelligent Priority Suggestions** based on multiple factors
- **Comprehensive Task Analysis** with productivity insights
- **Type-Safe AI Agents** with structured output schemas
- **Custom Tools** for database operations, priority calculation, and time analysis
- **In-Memory Storage** for demonstration purposes
- **Graceful Shutdown** and proper error handling

## 🏗️ Architecture

### Core Components

- **HTTP API Layer** - REST endpoints for task management
- **AI Agent Layer** - Intelligent analysis and recommendations
- **Custom Tools** - Database, priority calculation, and time tools
- **Storage Layer** - In-memory storage with sample data
- **Configuration** - Environment-based configuration management

### AI Agent Capabilities

The system includes two specialized AI agents:

1. **Task Analysis Agent** - Analyzes all tasks and provides insights
2. **Priority Suggestion Agent** - Suggests optimal priority for individual tasks

### Custom Tools

- **Database Tool** - CRUD operations on tasks with filtering
- **Priority Calculator Tool** - Algorithmic priority calculation
- **Time Tool** - Current time, date, and deadline analysis

## 🚀 Quick Start

### Prerequisites

- Go 1.21 or higher
- OpenAI API key

### Installation

1. Clone or download this project
2. Install dependencies:
   ```bash
   go mod tidy
   ```

3. Set your OpenAI API key:
   ```bash
   export OPENAI_API_KEY="your-openai-api-key-here"
   ```

4. Run the application:
   ```bash
   go run main.go
   ```

The server will start on port 8080 by default.

## 📖 API Documentation

### Task Management Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/health` | Health check |
| GET | `/tasks` | List all tasks |
| POST | `/tasks` | Create new task |
| GET | `/tasks/{id}` | Get specific task |
| PUT | `/tasks/{id}` | Update task |
| DELETE | `/tasks/{id}` | Delete task |

### AI-Powered Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/tasks/analyze` | AI analysis of all tasks |
| POST | `/tasks/{id}/suggest-priority` | AI priority suggestion for specific task |

## 💡 Example Usage

### Basic Task Operations

```bash
# Health check
curl http://localhost:8080/health

# List all tasks
curl http://localhost:8080/tasks

# Create a new task
curl -X POST http://localhost:8080/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Implement new feature",
    "description": "Add user authentication to the application",
    "priority": "high",
    "deadline": "2024-01-15T17:00:00Z"
  }'

# Update a task
curl -X PUT http://localhost:8080/tasks/task_1 \
  -H "Content-Type: application/json" \
  -d '{
    "status": "in_progress",
    "priority": "urgent"
  }'
```

### AI-Powered Features

```bash
# Get AI analysis of all tasks
curl -X POST http://localhost:8080/tasks/analyze

# Get AI priority suggestion for a specific task
curl -X POST http://localhost:8080/tasks/task_1/suggest-priority
```

## 🤖 AI Agent Examples

### Task Analysis Response

```json
{
  "overall_insights": [
    "You have 4 tasks with 1 urgent task requiring immediate attention",
    "Task distribution shows good priority balance but urgent items need focus",
    "One task is overdue and should be addressed immediately"
  ],
  "priority_recommendations": [
    {
      "task_id": "task_4",
      "current_priority": "urgent",
      "suggested_priority": "urgent",
      "reasoning": "Task is overdue and contains critical keywords"
    }
  ],
  "productivity_tips": [
    "Focus on the overdue urgent task first",
    "Consider breaking down large tasks into smaller subtasks",
    "Set specific deadlines for tasks without them"
  ],
  "urgent_tasks": ["task_4"],
  "task_distribution": {
    "by_priority": {"urgent": 1, "high": 1, "medium": 1, "low": 1},
    "by_status": {"pending": 3, "in_progress": 1},
    "total": 4
  }
}
```

### Priority Suggestion Response

```json
{
  "task_id": "task_1",
  "suggested_priority": "urgent",
  "reasoning": "Priority calculated as 'urgent' with a score of 85/100. Key factors considered: Deadline within 24 hours, Contains important keyword: client, Task is already in progress.",
  "factors": [
    "Deadline within 24 hours",
    "Contains important keyword: client",
    "Task is already in progress"
  ],
  "confidence": "high"
}
```

## 🛠️ Configuration

The application can be configured using environment variables:

| Variable | Default | Description |
|----------|---------|-------------|
| `PORT` | `8080` | Server port |
| `OPENAI_API_KEY` | *required* | OpenAI API key |
| `ENVIRONMENT` | `development` | Environment name |

## 🧪 Sample Data

The application starts with sample tasks to demonstrate functionality:

1. **Complete project proposal** (high priority, in progress, deadline tomorrow)
2. **Review team performance** (medium priority, pending, deadline next week)
3. **Update documentation** (low priority, pending, no deadline)
4. **Fix critical bug** (urgent priority, pending, overdue)

## 🔧 Development

### Project Structure

```
frameworkTestGO/
├── main.go                 # Application entry point
├── go.mod                  # Go module definition
├── config/
│   └── config.go          # Configuration management
├── models/
│   └── task.go            # Data models and schemas
├── handlers/
│   └── handlers.go        # HTTP request handlers
├── agents/
│   └── task_agent.go      # AI agent implementation
├── tools/
│   ├── database_tool.go   # Database operations tool
│   ├── priority_tool.go   # Priority calculation tool
│   └── time_tool.go       # Time analysis tool
├── storage/
│   └── memory_store.go    # In-memory storage
└── README.md              # This file
```

### Adding New Tools

To add a new tool for the AI agent:

1. Create a new file in the `tools/` directory
2. Implement the tool using the `llm.NewLLMTool` function
3. Add the tool to the agent in `agents/task_agent.go`
4. Set appropriate tool limits

### Extending the API

To add new endpoints:

1. Add handler methods to `handlers/handlers.go`
2. Register routes in the `SetupRoutes` method
3. Update the API documentation in `main.go`

## 🚀 Deployment

For production deployment:

1. Set environment variables appropriately
2. Use a proper database instead of in-memory storage
3. Add authentication and authorization
4. Implement proper logging and monitoring
5. Add rate limiting and security headers

## 📚 Learn More

- [go-agent Framework](https://github.com/vitalii-honchar/go-agent)
- [Building AI Agents in Go](https://vitaliihonchar.com/insights/go-ai-agent-library)
- [OpenAI API Documentation](https://platform.openai.com/docs)

## 📄 License

This project is provided as a proof of concept and demonstration of the go-agent framework.
