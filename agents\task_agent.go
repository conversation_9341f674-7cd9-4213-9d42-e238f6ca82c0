package agents

import (
	"context"
	"frameworkTestGO/models"
	"frameworkTestGO/storage"
	"frameworkTestGO/tools"
	"log"

	"github.com/vitalii-honchar/go-agent/pkg/goagent/agent"
	"github.com/vitalii-honchar/go-agent/pkg/goagent/llm"
)

// TaskAgent wraps the AI agent for task analysis
type TaskAgent struct {
	agent         *agent.Agent[models.TaskAnalysisResult]
	priorityAgent *agent.Agent[models.PrioritySuggestionResult]
}

// NewTaskAgent creates a new task analysis agent
func NewTaskAgent(apiKey string, store *storage.MemoryStore) (*TaskAgent, error) {
	// Create tools
	databaseTool := tools.CreateDatabaseTool(store)
	priorityTool := tools.CreatePriorityTool()
	timeTool := tools.CreateTimeTool()

	// Create logging middleware
	loggingMiddleware := createLoggingMiddleware()

	// Create main analysis agent
	analysisAgent, err := agent.NewAgent(
		agent.WithName[models.TaskAnalysisResult]("task_analysis_agent"),
		agent.WithLLMConfig[models.TaskAnalysisResult](llm.LLMConfig{
			Type:        llm.LLMTypeOpenAI,
			APIKey:      apiKey,
			Model:       "gpt-4",
			Temperature: 0.1,
		}),
		agent.WithBehavior[models.TaskAnalysisResult](getAnalysisBehavior()),
		agent.WithTool[models.TaskAnalysisResult]("database", databaseTool),
		agent.WithTool[models.TaskAnalysisResult]("priority_calculator", priorityTool),
		agent.WithTool[models.TaskAnalysisResult]("time", timeTool),
		agent.WithToolLimit[models.TaskAnalysisResult]("database", 5),
		agent.WithToolLimit[models.TaskAnalysisResult]("priority_calculator", 10),
		agent.WithToolLimit[models.TaskAnalysisResult]("time", 3),
		agent.WithMiddleware[models.TaskAnalysisResult](loggingMiddleware),
	)
	if err != nil {
		return nil, err
	}

	// Create priority suggestion agent
	priorityAgent, err := agent.NewAgent(
		agent.WithName[models.PrioritySuggestionResult]("priority_suggestion_agent"),
		agent.WithLLMConfig[models.PrioritySuggestionResult](llm.LLMConfig{
			Type:        llm.LLMTypeOpenAI,
			APIKey:      apiKey,
			Model:       "gpt-4",
			Temperature: 0.1,
		}),
		agent.WithBehavior[models.PrioritySuggestionResult](getPrioritySuggestionBehavior()),
		agent.WithTool[models.PrioritySuggestionResult]("priority_calculator", priorityTool),
		agent.WithTool[models.PrioritySuggestionResult]("time", timeTool),
		agent.WithToolLimit[models.PrioritySuggestionResult]("priority_calculator", 3),
		agent.WithToolLimit[models.PrioritySuggestionResult]("time", 2),
		agent.WithMiddleware[models.PrioritySuggestionResult](loggingMiddleware),
	)
	if err != nil {
		return nil, err
	}

	return &TaskAgent{
		agent:         analysisAgent,
		priorityAgent: priorityAgent,
	}, nil
}

// AnalyzeTasks performs comprehensive analysis of all tasks
func (ta *TaskAgent) AnalyzeTasks(ctx context.Context) (*models.TaskAnalysisResult, error) {
	log.Printf("🤖 Starting task analysis...")

	result, err := ta.agent.Run(ctx, "Analyze all tasks in the system and provide comprehensive insights")
	if err != nil {
		return nil, err
	}

	log.Printf("🤖 Task analysis completed")
	return result.Data, nil
}

// SuggestPriority suggests priority for a specific task
func (ta *TaskAgent) SuggestPriority(ctx context.Context, task *models.Task) (*models.PrioritySuggestionResult, error) {
	log.Printf("🤖 Suggesting priority for task: %s", task.Title)

	input := struct {
		Task models.Task `json:"task"`
	}{
		Task: *task,
	}

	result, err := ta.priorityAgent.Run(ctx, input)
	if err != nil {
		return nil, err
	}

	log.Printf("🤖 Priority suggestion completed")
	return result.Data, nil
}

// getAnalysisBehavior returns the behavior description for the analysis agent
func getAnalysisBehavior() string {
	return `You are an expert productivity consultant and task management specialist. Your role is to analyze task lists and provide actionable insights to improve productivity and task prioritization.

ANALYSIS PROCEDURE:
1. RETRIEVE TASKS: Use the database tool to get all tasks from the system
2. EXAMINE PATTERNS: Look for patterns in task priorities, statuses, and deadlines
3. CALCULATE PRIORITIES: Use the priority_calculator tool to analyze individual tasks
4. TIME ANALYSIS: Use the time tool to understand current context and deadlines
5. GENERATE INSIGHTS: Provide comprehensive analysis and recommendations

FOCUS AREAS:
- Task distribution by priority and status
- Overdue or urgent tasks that need immediate attention
- Priority misalignments (tasks that should have different priorities)
- Productivity bottlenecks and improvement opportunities
- Workload balance and time management suggestions

OUTPUT REQUIREMENTS:
- Provide 3-5 overall insights about the task list
- Identify specific priority recommendations for misaligned tasks
- Suggest 3-5 actionable productivity tips
- List urgent tasks that need immediate attention
- Include task distribution statistics

Be thorough but concise. Focus on actionable recommendations that will genuinely improve productivity.`
}

// getPrioritySuggestionBehavior returns the behavior description for the priority suggestion agent
func getPrioritySuggestionBehavior() string {
	return `You are a task prioritization expert. Your role is to analyze a single task and suggest the most appropriate priority level.

ANALYSIS PROCEDURE:
1. EXAMINE TASK: Analyze the task title, description, current priority, status, and deadline
2. CALCULATE PRIORITY: Use the priority_calculator tool to get algorithmic priority assessment
3. TIME CONTEXT: Use the time tool to understand current time context and deadline urgency
4. SYNTHESIZE: Combine algorithmic analysis with expert judgment

PRIORITY LEVELS:
- urgent: Immediate action required, critical impact
- high: Important tasks with near-term deadlines or high impact
- medium: Standard tasks with moderate importance
- low: Nice-to-have tasks with flexible timelines

FACTORS TO CONSIDER:
- Deadline proximity and urgency
- Keywords indicating importance or urgency
- Task age and how long it's been pending
- Current status and progress
- Business impact and stakeholder importance

OUTPUT REQUIREMENTS:
- Provide clear priority recommendation with confidence level
- Explain reasoning in detail
- List key factors that influenced the decision
- Consider both algorithmic and contextual factors

Be decisive but explain your reasoning clearly.`
}

// createLoggingMiddleware creates middleware for logging agent interactions
func createLoggingMiddleware() agent.AgentMiddleware {
	return func(ctx context.Context, state *agent.AgentState, llmMessage llm.LLMMessage) (llm.LLMMessage, error) {
		log.Printf("🤖 LLM Message [%d chars]: %s", len(llmMessage.Content), truncateContent(llmMessage.Content, 200))

		if len(llmMessage.ToolCalls) > 0 {
			log.Printf("🔧 Tool Calls: %d", len(llmMessage.ToolCalls))
			for i, toolCall := range llmMessage.ToolCalls {
				log.Printf("  #%d: %s(%s)", i+1, toolCall.ToolName, truncateContent(toolCall.Args, 100))
			}
		}

		if len(llmMessage.ToolResults) > 0 {
			log.Printf("📋 Tool Results: %d", len(llmMessage.ToolResults))
		}

		if llmMessage.End {
			log.Printf("🏁 LLM Execution Complete")
		}

		return llmMessage, nil
	}
}

// truncateContent truncates content for logging
func truncateContent(content string, maxLen int) string {
	if len(content) <= maxLen {
		return content
	}
	return content[:maxLen] + "..."
}
