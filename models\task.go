package models

import (
	"time"
)

// Task represents a task in our system
type Task struct {
	ID          string    `json:"id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Priority    string    `json:"priority"` // "low", "medium", "high", "urgent"
	Status      string    `json:"status"`   // "pending", "in_progress", "completed"
	Deadline    *time.Time `json:"deadline,omitempty"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// CreateTaskRequest represents the request to create a new task
type CreateTaskRequest struct {
	Title       string     `json:"title"`
	Description string     `json:"description"`
	Priority    string     `json:"priority,omitempty"`
	Deadline    *time.Time `json:"deadline,omitempty"`
}

// UpdateTaskRequest represents the request to update a task
type UpdateTaskRequest struct {
	Title       *string    `json:"title,omitempty"`
	Description *string    `json:"description,omitempty"`
	Priority    *string    `json:"priority,omitempty"`
	Status      *string    `json:"status,omitempty"`
	Deadline    *time.Time `json:"deadline,omitempty"`
}

// TaskAnalysisResult represents the AI agent's analysis of tasks
type TaskAnalysisResult struct {
	OverallInsights         []string                 `json:"overall_insights" jsonschema_description:"General insights about the task list"`
	PriorityRecommendations []PriorityRecommendation `json:"priority_recommendations" jsonschema_description:"Specific priority recommendations for tasks"`
	ProductivityTips        []string                 `json:"productivity_tips" jsonschema_description:"Tips to improve productivity"`
	UrgentTasks             []string                 `json:"urgent_tasks" jsonschema_description:"List of task IDs that need immediate attention"`
	TaskDistribution        TaskDistribution         `json:"task_distribution" jsonschema_description:"Analysis of task distribution by priority and status"`
}

// PriorityRecommendation represents a priority recommendation for a specific task
type PriorityRecommendation struct {
	TaskID            string `json:"task_id" jsonschema_description:"ID of the task"`
	CurrentPriority   string `json:"current_priority" jsonschema_description:"Current priority level"`
	SuggestedPriority string `json:"suggested_priority" jsonschema_description:"Recommended priority level"`
	Reasoning         string `json:"reasoning" jsonschema_description:"Explanation for the recommendation"`
}

// TaskDistribution represents the distribution of tasks by various categories
type TaskDistribution struct {
	ByPriority map[string]int `json:"by_priority" jsonschema_description:"Number of tasks by priority level"`
	ByStatus   map[string]int `json:"by_status" jsonschema_description:"Number of tasks by status"`
	Total      int            `json:"total" jsonschema_description:"Total number of tasks"`
}

// PrioritySuggestionResult represents the result of AI priority suggestion for a single task
type PrioritySuggestionResult struct {
	TaskID            string   `json:"task_id" jsonschema_description:"ID of the analyzed task"`
	SuggestedPriority string   `json:"suggested_priority" jsonschema_description:"AI-suggested priority level"`
	Reasoning         string   `json:"reasoning" jsonschema_description:"Detailed reasoning for the suggestion"`
	Factors           []string `json:"factors" jsonschema_description:"Key factors considered in the analysis"`
	Confidence        string   `json:"confidence" jsonschema_description:"Confidence level of the suggestion (low/medium/high)"`
}
