#!/bin/bash

# Bash script to test the Task Management API
# Make sure the server is running before executing this script

BASE_URL="http://localhost:8080"

echo "🧪 Testing Task Management API with AI Agent"
echo "============================================="

# Test 1: Health Check
echo -e "\n1. Health Check"
echo "GET $BASE_URL/health"
curl -s "$BASE_URL/health" | jq '.' || echo "❌ Health check failed"

# Test 2: List all tasks
echo -e "\n2. List All Tasks"
echo "GET $BASE_URL/tasks"
curl -s "$BASE_URL/tasks" | jq '.' || echo "❌ Failed to retrieve tasks"

# Test 3: Create a new task
echo -e "\n3. Create New Task"
echo "POST $BASE_URL/tasks"
NEW_TASK_JSON='{
  "title": "Test API Integration",
  "description": "Testing the task management API with bash/curl",
  "priority": "medium",
  "deadline": "'$(date -d '+3 days' -Iseconds)'"
}'

CREATED_TASK=$(curl -s -X POST "$BASE_URL/tasks" \
  -H "Content-Type: application/json" \
  -d "$NEW_TASK_JSON")

echo "$CREATED_TASK" | jq '.'
NEW_TASK_ID=$(echo "$CREATED_TASK" | jq -r '.id')

# Test 4: Get specific task
if [ "$NEW_TASK_ID" != "null" ] && [ -n "$NEW_TASK_ID" ]; then
    echo -e "\n4. Get Specific Task"
    echo "GET $BASE_URL/tasks/$NEW_TASK_ID"
    curl -s "$BASE_URL/tasks/$NEW_TASK_ID" | jq '.' || echo "❌ Failed to retrieve task"
fi

# Test 5: AI Task Analysis
echo -e "\n5. AI Task Analysis"
echo "⏳ This may take a moment as the AI analyzes all tasks..."
echo "POST $BASE_URL/tasks/analyze"
curl -s -X POST "$BASE_URL/tasks/analyze" | jq '.' || echo "❌ AI analysis failed - make sure OPENAI_API_KEY is set"

# Test 6: AI Priority Suggestion
echo -e "\n6. AI Priority Suggestion"
echo "⏳ Getting AI priority suggestion for first task..."
echo "POST $BASE_URL/tasks/task_1/suggest-priority"
curl -s -X POST "$BASE_URL/tasks/task_1/suggest-priority" | jq '.' || echo "❌ Priority suggestion failed - make sure OPENAI_API_KEY is set"

# Test 7: Update task
if [ "$NEW_TASK_ID" != "null" ] && [ -n "$NEW_TASK_ID" ]; then
    echo -e "\n7. Update Task"
    echo "PUT $BASE_URL/tasks/$NEW_TASK_ID"
    UPDATE_JSON='{
      "status": "in_progress",
      "priority": "high"
    }'
    
    curl -s -X PUT "$BASE_URL/tasks/$NEW_TASK_ID" \
      -H "Content-Type: application/json" \
      -d "$UPDATE_JSON" | jq '.' || echo "❌ Failed to update task"
fi

echo -e "\n🎉 API Testing Complete!"
echo "Check the server logs to see the AI agent in action."
