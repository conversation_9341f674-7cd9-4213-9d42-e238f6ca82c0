package storage

import (
	"fmt"
	"frameworkTestGO/models"
	"sync"
	"time"
)

// MemoryStore provides in-memory storage for tasks
type MemoryStore struct {
	tasks map[string]*models.Task
	mutex sync.RWMutex
	idCounter int
}

// NewMemoryStore creates a new in-memory store
func NewMemoryStore() *MemoryStore {
	store := &MemoryStore{
		tasks: make(map[string]*models.Task),
		mutex: sync.RWMutex{},
		idCounter: 0,
	}
	
	// Add some sample tasks for demo purposes
	store.addSampleTasks()
	
	return store
}

// CreateTask creates a new task
func (s *MemoryStore) CreateTask(req models.CreateTaskRequest) (*models.Task, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	s.idCounter++
	id := fmt.Sprintf("task_%d", s.idCounter)
	
	priority := req.Priority
	if priority == "" {
		priority = "medium"
	}
	
	task := &models.Task{
		ID:          id,
		Title:       req.Title,
		Description: req.Description,
		Priority:    priority,
		Status:      "pending",
		Deadline:    req.Deadline,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	
	s.tasks[id] = task
	return task, nil
}

// GetTask retrieves a task by ID
func (s *MemoryStore) GetTask(id string) (*models.Task, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	
	task, exists := s.tasks[id]
	if !exists {
		return nil, fmt.Errorf("task with ID %s not found", id)
	}
	
	return task, nil
}

// GetAllTasks retrieves all tasks
func (s *MemoryStore) GetAllTasks() ([]*models.Task, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	
	tasks := make([]*models.Task, 0, len(s.tasks))
	for _, task := range s.tasks {
		tasks = append(tasks, task)
	}
	
	return tasks, nil
}

// UpdateTask updates an existing task
func (s *MemoryStore) UpdateTask(id string, req models.UpdateTaskRequest) (*models.Task, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	task, exists := s.tasks[id]
	if !exists {
		return nil, fmt.Errorf("task with ID %s not found", id)
	}
	
	if req.Title != nil {
		task.Title = *req.Title
	}
	if req.Description != nil {
		task.Description = *req.Description
	}
	if req.Priority != nil {
		task.Priority = *req.Priority
	}
	if req.Status != nil {
		task.Status = *req.Status
	}
	if req.Deadline != nil {
		task.Deadline = req.Deadline
	}
	
	task.UpdatedAt = time.Now()
	return task, nil
}

// DeleteTask deletes a task by ID
func (s *MemoryStore) DeleteTask(id string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	if _, exists := s.tasks[id]; !exists {
		return fmt.Errorf("task with ID %s not found", id)
	}
	
	delete(s.tasks, id)
	return nil
}

// addSampleTasks adds some sample tasks for demonstration
func (s *MemoryStore) addSampleTasks() {
	now := time.Now()
	tomorrow := now.Add(24 * time.Hour)
	nextWeek := now.Add(7 * 24 * time.Hour)
	
	sampleTasks := []*models.Task{
		{
			ID:          "task_1",
			Title:       "Complete project proposal",
			Description: "Write and submit the Q1 project proposal for the new client",
			Priority:    "high",
			Status:      "in_progress",
			Deadline:    &tomorrow,
			CreatedAt:   now.Add(-2 * time.Hour),
			UpdatedAt:   now.Add(-1 * time.Hour),
		},
		{
			ID:          "task_2",
			Title:       "Review team performance",
			Description: "Conduct quarterly performance reviews for all team members",
			Priority:    "medium",
			Status:      "pending",
			Deadline:    &nextWeek,
			CreatedAt:   now.Add(-1 * time.Hour),
			UpdatedAt:   now.Add(-1 * time.Hour),
		},
		{
			ID:          "task_3",
			Title:       "Update documentation",
			Description: "Update API documentation with latest changes",
			Priority:    "low",
			Status:      "pending",
			CreatedAt:   now.Add(-30 * time.Minute),
			UpdatedAt:   now.Add(-30 * time.Minute),
		},
		{
			ID:          "task_4",
			Title:       "Fix critical bug",
			Description: "Resolve the authentication issue reported by users",
			Priority:    "urgent",
			Status:      "pending",
			Deadline:    &now,
			CreatedAt:   now.Add(-10 * time.Minute),
			UpdatedAt:   now.Add(-10 * time.Minute),
		},
	}
	
	for _, task := range sampleTasks {
		s.tasks[task.ID] = task
	}
	
	s.idCounter = 4
}
