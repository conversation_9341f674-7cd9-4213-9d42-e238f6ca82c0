package main

import (
	"context"
	"frameworkTestGO/agents"
	"frameworkTestGO/config"
	"frameworkTestGO/handlers"
	"frameworkTestGO/storage"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"
)

func main() {
	// Load configuration
	cfg := config.Load()
	if err := cfg.Validate(); err != nil {
		log.Fatalf("Configuration error: %v", err)
	}
	
	log.Printf("Starting Task Management API with AI Agent...")
	log.Printf("Environment: %s", cfg.Environment)
	log.Printf("Port: %s", cfg.Port)
	
	// Initialize storage
	store := storage.NewMemoryStore()
	log.Printf("✅ Memory store initialized with sample data")
	
	// Initialize AI agent
	taskAgent, err := agents.NewTaskAgent(cfg.OpenAIAPIKey, store)
	if err != nil {
		log.Fatalf("Failed to create task agent: %v", err)
	}
	log.Printf("✅ AI task agent initialized")
	
	// Initialize handlers
	taskHandler := handlers.NewTaskHandler(store, taskAgent)
	router := taskHandler.SetupRoutes()
	log.Printf("✅ HTTP routes configured")
	
	// Create HTTP server
	server := &http.Server{
		Addr:         ":" + cfg.Port,
		Handler:      router,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}
	
	// Start server in a goroutine
	go func() {
		log.Printf("🚀 Server starting on port %s", cfg.Port)
		log.Printf("📖 API Documentation:")
		printAPIDocumentation(cfg.Port)
		
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Server failed to start: %v", err)
		}
	}()
	
	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	
	log.Printf("🛑 Shutting down server...")
	
	// Graceful shutdown with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}
	
	log.Printf("✅ Server exited")
}

func printAPIDocumentation(port string) {
	baseURL := "http://localhost:" + port
	
	log.Printf("")
	log.Printf("📋 TASK MANAGEMENT ENDPOINTS:")
	log.Printf("  GET    %s/health                     - Health check", baseURL)
	log.Printf("  GET    %s/tasks                      - List all tasks", baseURL)
	log.Printf("  POST   %s/tasks                      - Create new task", baseURL)
	log.Printf("  GET    %s/tasks/{id}                 - Get specific task", baseURL)
	log.Printf("  PUT    %s/tasks/{id}                 - Update task", baseURL)
	log.Printf("  DELETE %s/tasks/{id}                 - Delete task", baseURL)
	log.Printf("")
	log.Printf("🤖 AI-POWERED ENDPOINTS:")
	log.Printf("  POST   %s/tasks/analyze              - AI analysis of all tasks", baseURL)
	log.Printf("  POST   %s/tasks/{id}/suggest-priority - AI priority suggestion", baseURL)
	log.Printf("")
	log.Printf("💡 EXAMPLE REQUESTS:")
	log.Printf("  curl %s/health", baseURL)
	log.Printf("  curl %s/tasks", baseURL)
	log.Printf("  curl -X POST %s/tasks/analyze", baseURL)
	log.Printf("  curl -X POST %s/tasks/task_1/suggest-priority", baseURL)
	log.Printf("")
	log.Printf("📝 CREATE TASK EXAMPLE:")
	log.Printf(`  curl -X POST %s/tasks \`, baseURL)
	log.Printf(`    -H "Content-Type: application/json" \`)
	log.Printf(`    -d '{"title":"New Task","description":"Task description","priority":"medium"}'`)
	log.Printf("")
}
