package handlers

import (
	"context"
	"encoding/json"
	"frameworkTestGO/agents"
	"frameworkTestGO/models"
	"frameworkTestGO/storage"
	"log"
	"net/http"
	"time"

	"github.com/gorilla/mux"
)

// TaskHandler handles HTTP requests for task management
type TaskHandler struct {
	store     *storage.MemoryStore
	taskAgent *agents.TaskAgent
}

// NewTaskHandler creates a new task handler
func NewTaskHandler(store *storage.MemoryStore, taskAgent *agents.TaskAgent) *TaskHandler {
	return &TaskHandler{
		store:     store,
		taskAgent: taskAgent,
	}
}

// CreateTask handles POST /tasks
func (h *TaskHandler) CreateTask(w http.ResponseWriter, r *http.Request) {
	var req models.CreateTaskRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}
	
	if req.Title == "" {
		http.Error(w, "Title is required", http.StatusBadRequest)
		return
	}
	
	task, err := h.store.CreateTask(req)
	if err != nil {
		log.Printf("Error creating task: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(task)
}

// GetTasks handles GET /tasks
func (h *TaskHandler) GetTasks(w http.ResponseWriter, r *http.Request) {
	tasks, err := h.store.GetAllTasks()
	if err != nil {
		log.Printf("Error getting tasks: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(tasks)
}

// GetTask handles GET /tasks/{id}
func (h *TaskHandler) GetTask(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]
	
	task, err := h.store.GetTask(id)
	if err != nil {
		http.Error(w, "Task not found", http.StatusNotFound)
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(task)
}

// UpdateTask handles PUT /tasks/{id}
func (h *TaskHandler) UpdateTask(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]
	
	var req models.UpdateTaskRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}
	
	task, err := h.store.UpdateTask(id, req)
	if err != nil {
		if err.Error() == "task with ID "+id+" not found" {
			http.Error(w, "Task not found", http.StatusNotFound)
		} else {
			log.Printf("Error updating task: %v", err)
			http.Error(w, "Internal server error", http.StatusInternalServerError)
		}
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(task)
}

// DeleteTask handles DELETE /tasks/{id}
func (h *TaskHandler) DeleteTask(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]
	
	err := h.store.DeleteTask(id)
	if err != nil {
		if err.Error() == "task with ID "+id+" not found" {
			http.Error(w, "Task not found", http.StatusNotFound)
		} else {
			log.Printf("Error deleting task: %v", err)
			http.Error(w, "Internal server error", http.StatusInternalServerError)
		}
		return
	}
	
	w.WriteHeader(http.StatusNoContent)
}

// AnalyzeTasks handles POST /tasks/analyze
func (h *TaskHandler) AnalyzeTasks(w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), 60*time.Second)
	defer cancel()
	
	log.Printf("Starting task analysis...")
	result, err := h.taskAgent.AnalyzeTasks(ctx)
	if err != nil {
		log.Printf("Error analyzing tasks: %v", err)
		http.Error(w, "Failed to analyze tasks", http.StatusInternalServerError)
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(result)
}

// SuggestPriority handles POST /tasks/{id}/suggest-priority
func (h *TaskHandler) SuggestPriority(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]
	
	task, err := h.store.GetTask(id)
	if err != nil {
		http.Error(w, "Task not found", http.StatusNotFound)
		return
	}
	
	ctx, cancel := context.WithTimeout(r.Context(), 30*time.Second)
	defer cancel()
	
	log.Printf("Suggesting priority for task: %s", task.Title)
	result, err := h.taskAgent.SuggestPriority(ctx, task)
	if err != nil {
		log.Printf("Error suggesting priority: %v", err)
		http.Error(w, "Failed to suggest priority", http.StatusInternalServerError)
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(result)
}

// HealthCheck handles GET /health
func (h *TaskHandler) HealthCheck(w http.ResponseWriter, r *http.Request) {
	response := map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now().Format(time.RFC3339),
		"service":   "task-management-api",
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// SetupRoutes configures all HTTP routes
func (h *TaskHandler) SetupRoutes() *mux.Router {
	r := mux.NewRouter()
	
	// Health check
	r.HandleFunc("/health", h.HealthCheck).Methods("GET")
	
	// Task CRUD operations
	r.HandleFunc("/tasks", h.CreateTask).Methods("POST")
	r.HandleFunc("/tasks", h.GetTasks).Methods("GET")
	r.HandleFunc("/tasks/{id}", h.GetTask).Methods("GET")
	r.HandleFunc("/tasks/{id}", h.UpdateTask).Methods("PUT")
	r.HandleFunc("/tasks/{id}", h.DeleteTask).Methods("DELETE")
	
	// AI-powered endpoints
	r.HandleFunc("/tasks/analyze", h.AnalyzeTasks).Methods("POST")
	r.HandleFunc("/tasks/{id}/suggest-priority", h.SuggestPriority).Methods("POST")
	
	// Add CORS middleware
	r.Use(corsMiddleware)
	r.Use(loggingMiddleware)
	
	return r
}

// corsMiddleware adds CORS headers
func corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}
		
		next.ServeHTTP(w, r)
	})
}

// loggingMiddleware logs HTTP requests
func loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		
		next.ServeHTTP(w, r)
		
		log.Printf("%s %s %v", r.Method, r.URL.Path, time.Since(start))
	})
}
