package tools

import (
	"frameworkTestGO/models"
	"frameworkTestGO/storage"
	"log"

	"github.com/vitalii-honchar/go-agent/pkg/goagent/llm"
)

// DatabaseToolParams represents parameters for database operations
type DatabaseToolParams struct {
	Operation string `json:"operation" jsonschema_description:"Database operation: 'get_all', 'get_by_id', 'get_by_status', 'get_by_priority'"`
	TaskID    string `json:"task_id,omitempty" jsonschema_description:"Task ID for get_by_id operation"`
	Status    string `json:"status,omitempty" jsonschema_description:"Status filter for get_by_status operation"`
	Priority  string `json:"priority,omitempty" jsonschema_description:"Priority filter for get_by_priority operation"`
}

// DatabaseToolResult represents the result of database operations
type DatabaseToolResult struct {
	llm.BaseLLMToolResult
	Tasks []models.Task `json:"tasks" jsonschema_description:"List of tasks returned by the operation"`
	Count int           `json:"count" jsonschema_description:"Number of tasks returned"`
	Error string        `json:"error,omitempty" jsonschema_description:"Error message if operation failed"`
}

// CreateDatabaseTool creates a new database tool for the AI agent
func CreateDatabaseTool(store *storage.MemoryStore) llm.LLMTool {
	tool, err := llm.NewLLMTool(
		llm.WithLLMToolName("database"),
		llm.WithLLMToolDescription("Access task database to retrieve tasks by various criteria"),
		llm.WithLLMToolParametersSchema[DatabaseToolParams](),
		llm.WithLLMToolCall(func(callID string, params DatabaseToolParams) (DatabaseToolResult, error) {
			log.Printf("🗄️  DATABASE TOOL: %s", params.Operation)

			result := DatabaseToolResult{
				BaseLLMToolResult: llm.BaseLLMToolResult{ID: callID},
			}

			switch params.Operation {
			case "get_all":
				tasks, err := store.GetAllTasks()
				if err != nil {
					result.Error = err.Error()
					return result, nil
				}
				result.Tasks = convertTaskPointers(tasks)
				result.Count = len(result.Tasks)

			case "get_by_id":
				if params.TaskID == "" {
					result.Error = "task_id is required for get_by_id operation"
					return result, nil
				}
				task, err := store.GetTask(params.TaskID)
				if err != nil {
					result.Error = err.Error()
					return result, nil
				}
				result.Tasks = []models.Task{*task}
				result.Count = 1

			case "get_by_status":
				if params.Status == "" {
					result.Error = "status is required for get_by_status operation"
					return result, nil
				}
				tasks, err := store.GetAllTasks()
				if err != nil {
					result.Error = err.Error()
					return result, nil
				}
				filtered := filterTasksByStatus(tasks, params.Status)
				result.Tasks = convertTaskPointers(filtered)
				result.Count = len(result.Tasks)

			case "get_by_priority":
				if params.Priority == "" {
					result.Error = "priority is required for get_by_priority operation"
					return result, nil
				}
				tasks, err := store.GetAllTasks()
				if err != nil {
					result.Error = err.Error()
					return result, nil
				}
				filtered := filterTasksByPriority(tasks, params.Priority)
				result.Tasks = convertTaskPointers(filtered)
				result.Count = len(result.Tasks)

			default:
				result.Error = "unknown operation: " + params.Operation
			}

			log.Printf("🗄️  DATABASE RESULT: %d tasks returned", result.Count)
			return result, nil
		}),
	)

	if err != nil {
		log.Fatalf("Failed to create database tool: %v", err)
	}

	return tool
}

// Helper functions

func convertTaskPointers(tasks []*models.Task) []models.Task {
	result := make([]models.Task, len(tasks))
	for i, task := range tasks {
		result[i] = *task
	}
	return result
}

func filterTasksByStatus(tasks []*models.Task, status string) []*models.Task {
	var filtered []*models.Task
	for _, task := range tasks {
		if task.Status == status {
			filtered = append(filtered, task)
		}
	}
	return filtered
}

func filterTasksByPriority(tasks []*models.Task, priority string) []*models.Task {
	var filtered []*models.Task
	for _, task := range tasks {
		if task.Priority == priority {
			filtered = append(filtered, task)
		}
	}
	return filtered
}
